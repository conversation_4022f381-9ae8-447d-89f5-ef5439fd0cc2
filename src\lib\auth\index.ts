// Authentication and Authorization utilities

import { User } from 'payload/generated-types'
import { PayloadRequest } from 'payload'

// User roles with hierarchical permissions
export const USER_ROLES = {
  'super-admin': {
    level: 5,
    permissions: ['*'],
    description: 'Full system access',
  },
  'admin': {
    level: 4,
    permissions: [
      'create', 'read', 'update', 'delete',
      'manage-users', 'manage-settings', 'publish', 'archive'
    ],
    description: 'Content and user management',
  },
  'content-manager': {
    level: 3,
    permissions: ['create', 'read', 'update', 'publish', 'manage-media'],
    description: 'Content creation and publishing',
  },
  'editor': {
    level: 2,
    permissions: ['read', 'update', 'create-draft'],
    description: 'Content editing only',
  },
  'user': {
    level: 1,
    permissions: ['read'],
    description: 'Read-only access',
  },
} as const

export type UserRole = keyof typeof USER_ROLES
export type Permission = string

// Check if user has specific permission
export function hasPermission(user: User | null, permission: Permission): boolean {
  if (!user || !user.role) return false
  
  const userRole = USER_ROLES[user.role as UserRole]
  if (!userRole) return false
  
  // Super admin has all permissions
  if (userRole.permissions.includes('*')) return true
  
  // Check specific permission
  return userRole.permissions.includes(permission)
}

// Check if user has any of the specified permissions
export function hasAnyPermission(user: User | null, permissions: Permission[]): boolean {
  return permissions.some(permission => hasPermission(user, permission))
}

// Check if user has all specified permissions
export function hasAllPermissions(user: User | null, permissions: Permission[]): boolean {
  return permissions.every(permission => hasPermission(user, permission))
}

// Check if user role level meets minimum requirement
export function hasMinimumRoleLevel(user: User | null, minimumLevel: number): boolean {
  if (!user || !user.role) return false
  
  const userRole = USER_ROLES[user.role as UserRole]
  if (!userRole) return false
  
  return userRole.level >= minimumLevel
}

// Access control functions for PayloadCMS
export const isAdmin = ({ req: { user } }: { req: PayloadRequest }) => {
  return hasPermission(user, 'manage-users')
}

export const isAdminOrSelf = ({ req: { user } }: { req: PayloadRequest }) => {
  if (hasPermission(user, 'manage-users')) return true
  
  return {
    id: {
      equals: user?.id,
    },
  }
}

export const canCreate = ({ req: { user } }: { req: PayloadRequest }) => {
  return hasPermission(user, 'create')
}

export const canRead = ({ req: { user } }: { req: PayloadRequest }) => {
  return hasPermission(user, 'read')
}

export const canUpdate = ({ req: { user } }: { req: PayloadRequest }) => {
  return hasPermission(user, 'update')
}

export const canDelete = ({ req: { user } }: { req: PayloadRequest }) => {
  return hasPermission(user, 'delete')
}

export const canPublish = ({ req: { user } }: { req: PayloadRequest }) => {
  return hasPermission(user, 'publish')
}

// Read published content or authenticated users can read all
export const readPublishedOrAuthenticated = ({ req: { user } }: { req: PayloadRequest }) => {
  if (user) return true
  
  return {
    published: {
      equals: true,
    },
  }
}

// Admin or content manager access
export const adminOrContentManager = ({ req: { user } }: { req: PayloadRequest }) => {
  return hasAnyPermission(user, ['manage-users', 'publish'])
}

// Editor level access
export const editorAccess = ({ req: { user } }: { req: PayloadRequest }) => {
  return hasMinimumRoleLevel(user, 2)
}

// Frontend authentication utilities
export class AuthManager {
  private static instance: AuthManager
  private token: string | null = null
  private user: User | null = null
  private refreshTimer: NodeJS.Timeout | null = null

  private constructor() {
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('auth-token')
      const userData = localStorage.getItem('user')
      if (userData) {
        try {
          this.user = JSON.parse(userData)
        } catch {
          this.user = null
        }
      }
    }
  }

  static getInstance(): AuthManager {
    if (!AuthManager.instance) {
      AuthManager.instance = new AuthManager()
    }
    return AuthManager.instance
  }

  // Login user
  async login(email: string, password: string): Promise<{ user: User; token: string }> {
    try {
      const response = await fetch('/api/users/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Login failed')
      }

      const data = await response.json()
      
      this.setAuthData(data.token, data.user)
      this.startTokenRefresh()
      
      return data
    } catch (error) {
      console.error('Login error:', error)
      throw error
    }
  }

  // Logout user
  async logout(): Promise<void> {
    try {
      if (this.token) {
        await fetch('/api/users/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.token}`,
          },
        })
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      this.clearAuthData()
    }
  }

  // Refresh token
  async refreshToken(): Promise<void> {
    if (!this.token) return

    try {
      const response = await fetch('/api/users/refresh-token', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`,
        },
      })

      if (response.ok) {
        const data = await response.json()
        this.setAuthData(data.token, data.user)
      } else {
        // Token refresh failed, logout user
        this.clearAuthData()
      }
    } catch (error) {
      console.error('Token refresh error:', error)
      this.clearAuthData()
    }
  }

  // Get current user
  getUser(): User | null {
    return this.user
  }

  // Get auth token
  getToken(): string | null {
    return this.token
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    if (!this.token || !this.user) return false
    
    try {
      const payload = JSON.parse(atob(this.token.split('.')[1]))
      return payload.exp > Date.now() / 1000
    } catch {
      return false
    }
  }

  // Check user permissions
  hasPermission(permission: Permission): boolean {
    return hasPermission(this.user, permission)
  }

  hasAnyPermission(permissions: Permission[]): boolean {
    return hasAnyPermission(this.user, permissions)
  }

  hasAllPermissions(permissions: Permission[]): boolean {
    return hasAllPermissions(this.user, permissions)
  }

  hasMinimumRoleLevel(level: number): boolean {
    return hasMinimumRoleLevel(this.user, level)
  }

  // Get auth headers for API requests
  getAuthHeaders(): HeadersInit {
    return {
      'Content-Type': 'application/json',
      ...(this.token && { 'Authorization': `Bearer ${this.token}` }),
    }
  }

  // Private methods
  private setAuthData(token: string, user: User): void {
    this.token = token
    this.user = user
    
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth-token', token)
      localStorage.setItem('user', JSON.stringify(user))
    }
  }

  private clearAuthData(): void {
    this.token = null
    this.user = null
    
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
      this.refreshTimer = null
    }
    
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth-token')
      localStorage.removeItem('user')
    }
  }

  private startTokenRefresh(): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
    }
    
    // Refresh token every 50 minutes (tokens typically expire in 1 hour)
    this.refreshTimer = setTimeout(() => {
      this.refreshToken()
      this.startTokenRefresh()
    }, 50 * 60 * 1000)
  }
}

// Export singleton instance
export const authManager = AuthManager.getInstance()

// React hook for authentication
export function useAuth() {
  const [user, setUser] = useState<User | null>(authManager.getUser())
  const [isAuthenticated, setIsAuthenticated] = useState(authManager.isAuthenticated())
  const [loading, setLoading] = useState(false)

  const login = async (email: string, password: string) => {
    setLoading(true)
    try {
      const result = await authManager.login(email, password)
      setUser(result.user)
      setIsAuthenticated(true)
      return result
    } catch (error) {
      throw error
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    setLoading(true)
    try {
      await authManager.logout()
      setUser(null)
      setIsAuthenticated(false)
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setLoading(false)
    }
  }

  const hasPermission = (permission: Permission) => {
    return authManager.hasPermission(permission)
  }

  const hasAnyPermission = (permissions: Permission[]) => {
    return authManager.hasAnyPermission(permissions)
  }

  const hasAllPermissions = (permissions: Permission[]) => {
    return authManager.hasAllPermissions(permissions)
  }

  const hasMinimumRoleLevel = (level: number) => {
    return authManager.hasMinimumRoleLevel(level)
  }

  useEffect(() => {
    // Listen for auth state changes
    const checkAuth = () => {
      const currentUser = authManager.getUser()
      const currentAuthState = authManager.isAuthenticated()
      
      if (currentUser !== user) {
        setUser(currentUser)
      }
      
      if (currentAuthState !== isAuthenticated) {
        setIsAuthenticated(currentAuthState)
      }
    }

    // Check auth state periodically
    const interval = setInterval(checkAuth, 5000)
    
    return () => clearInterval(interval)
  }, [user, isAuthenticated])

  return {
    user,
    isAuthenticated,
    loading,
    login,
    logout,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasMinimumRoleLevel,
  }
}

// Protected route component
export function ProtectedRoute({ 
  children, 
  requiredPermissions = [], 
  requiredRoleLevel,
  fallback = <div>Access denied</div> 
}: {
  children: React.ReactNode
  requiredPermissions?: Permission[]
  requiredRoleLevel?: number
  fallback?: React.ReactNode
}) {
  const { isAuthenticated, hasAllPermissions, hasMinimumRoleLevel } = useAuth()

  if (!isAuthenticated) {
    return <div>Please log in to access this page</div>
  }

  if (requiredPermissions.length > 0 && !hasAllPermissions(requiredPermissions)) {
    return fallback
  }

  if (requiredRoleLevel && !hasMinimumRoleLevel(requiredRoleLevel)) {
    return fallback
  }

  return <>{children}</>
}
