# Complete Guide: Browser Testing with PostgreSQL

## Overview

This comprehensive guide covers browser testing, PostgreSQL configuration, and detailed CRUD operations for the Natural Products Institute CMS.

## 🗄️ PostgreSQL Setup

### 1. Database Configuration

The CMS is already configured for PostgreSQL. Your environment should include:

```env
# PostgreSQL Configuration
DATABASE_URI=postgresql://username:password@localhost:5432/npi_cms

# For cloud databases (Supabase, AWS RDS, etc.)
DATABASE_URI=********************************/dbname?sslmode=require

# PayloadCMS Configuration
PAYLOAD_SECRET=your-super-secure-secret-key-min-32-chars
JWT_SECRET=your-jwt-secret-key-min-32-chars

# Application URLs
NEXT_PUBLIC_API_URL=http://localhost:3000
PAYLOAD_PUBLIC_SERVER_URL=http://localhost:3000

# File Storage
BLOB_READ_WRITE_TOKEN=your-vercel-blob-token
```

### 2. Database Schema

PayloadCMS automatically creates and manages the PostgreSQL schema. Key tables include:

- `projects` - Project data and metadata
- `success_stories` - Success story content
- `resources` - Resource files and metadata
- `news` - News articles and updates
- `media` - Media files and gallery items
- `contact_submissions` - Contact form submissions
- `users` - User accounts and authentication
- `payload_preferences` - User preferences
- `payload_migrations` - Schema migration history

### 3. Local PostgreSQL Setup

```bash
# Install PostgreSQL (Ubuntu/Debian)
sudo apt update
sudo apt install postgresql postgresql-contrib

# Start PostgreSQL service
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create database and user
sudo -u postgres psql
CREATE DATABASE npi_cms;
CREATE USER npi_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE npi_cms TO npi_user;
\q

# Update your .env file with the connection string
DATABASE_URI=postgresql://npi_user:your_password@localhost:5432/npi_cms
```

## 🌐 Browser Testing

### 1. Interactive API Testing Dashboard

**Access URL**: `http://localhost:3000/test-api`

Features:
- **Authentication Testing**: Login directly from the interface
- **All Endpoints**: Test all public and protected endpoints
- **Real-time Results**: See response times and data
- **Error Handling**: View detailed error messages
- **Test Summary**: Success/failure statistics

### 2. Admin Panel Testing

**Access URL**: `http://localhost:3000/admin`

**Default Credentials**:
- Email: `<EMAIL>`
- Password: `admin123` (change immediately)

**Test Scenarios**:
1. **User Authentication**
   - Login with different user roles
   - Test password reset functionality
   - Verify session management

2. **Content Management**
   - Create, edit, delete projects
   - Upload and manage media files
   - Test rich text editor functionality
   - Verify form validation

3. **Access Control**
   - Test role-based permissions
   - Verify protected routes
   - Check data visibility by role

### 3. Manual API Testing

Use browser developer tools (F12) for manual testing:

```javascript
// Test public endpoints
fetch('/api/projects?featured=true&limit=5')
  .then(response => response.json())
  .then(data => console.log('Featured Projects:', data))

// Test authentication
fetch('/api/users/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'admin123'
  })
})
.then(response => response.json())
.then(data => {
  console.log('Login Response:', data)
  localStorage.setItem('auth-token', data.token)
})

// Test protected endpoints
fetch('/api/admin/projects', {
  headers: {
    'Authorization': 'Bearer ' + localStorage.getItem('auth-token')
  }
})
.then(response => response.json())
.then(data => console.log('Admin Projects:', data))
```

## 🔐 Authentication & Access Control

### User Roles Hierarchy

```typescript
const USER_ROLES = {
  'super-admin': {
    level: 5,
    permissions: ['*'], // All permissions
    description: 'Full system access'
  },
  'admin': {
    level: 4,
    permissions: ['create', 'read', 'update', 'delete', 'manage-users'],
    description: 'Content and user management'
  },
  'content-manager': {
    level: 3,
    permissions: ['create', 'read', 'update', 'publish'],
    description: 'Content creation and publishing'
  },
  'editor': {
    level: 2,
    permissions: ['read', 'update'],
    description: 'Content editing only'
  },
  'user': {
    level: 1,
    permissions: ['read'],
    description: 'Read-only access'
  }
}
```

### Testing Authentication

```javascript
// Login and store token
async function testLogin() {
  const response = await fetch('/api/users/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'admin123'
    })
  })
  
  const data = await response.json()
  if (data.token) {
    localStorage.setItem('auth-token', data.token)
    localStorage.setItem('user', JSON.stringify(data.user))
    console.log('Login successful:', data.user)
  }
}

// Test protected endpoint
async function testProtectedEndpoint() {
  const token = localStorage.getItem('auth-token')
  
  const response = await fetch('/api/admin/contact-submissions', {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  })
  
  const data = await response.json()
  console.log('Protected data:', data)
}
```

## 📝 CRUD Operations Testing

### 1. Projects CRUD

#### Create Project
```javascript
async function createProject() {
  const projectData = {
    title: 'Test Project from Browser',
    description: {
      root: {
        children: [{
          children: [{
            detail: 0,
            format: 0,
            mode: 'normal',
            style: '',
            text: 'This is a test project created from browser testing.',
            type: 'text',
            version: 1
          }],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'paragraph',
          version: 1
        }],
        direction: 'ltr',
        format: '',
        indent: 0,
        type: 'root',
        version: 1
      }
    },
    summary: 'A test project for browser testing',
    category: 'community-empowerment',
    pillar: 'community-innovation',
    status: 'active',
    timeline: {
      startDate: new Date().toISOString()
    },
    featured: false,
    published: true,
    slug: 'test-project-' + Date.now()
  }

  const response = await fetch('/api/projects', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('auth-token')}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(projectData)
  })

  const result = await response.json()
  console.log('Project created:', result)
  return result
}
```

#### Read Projects
```javascript
async function getProjects(filters = {}) {
  const params = new URLSearchParams(filters)
  
  const response = await fetch(`/api/projects?${params}`)
  const data = await response.json()
  
  console.log('Projects:', data)
  return data
}

// Test different filters
getProjects({ featured: true, limit: 5 })
getProjects({ category: 'community-empowerment', status: 'active' })
getProjects({ search: 'community', page: 1, limit: 10 })
```

#### Update Project
```javascript
async function updateProject(projectId, updates) {
  const response = await fetch(`/api/projects/${projectId}`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('auth-token')}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(updates)
  })

  const result = await response.json()
  console.log('Project updated:', result)
  return result
}

// Example update
updateProject('project-id', {
  status: 'completed',
  featured: true
})
```

#### Delete Project
```javascript
async function deleteProject(projectId) {
  const response = await fetch(`/api/projects/${projectId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('auth-token')}`
    }
  })

  const result = await response.json()
  console.log('Project deleted:', result)
  return result
}
```

### 2. Contact Form Testing (Public)

```javascript
async function testContactForm() {
  const formData = {
    name: 'Test User',
    email: '<EMAIL>',
    phone: '+254700000000',
    organization: 'Test Organization',
    role: 'Tester',
    subject: 'Browser Testing Inquiry',
    category: 'general',
    priority: 'medium',
    message: 'This is a test message submitted through browser testing.',
    location: {
      county: 'nairobi',
      city: 'Nairobi',
      country: 'Kenya'
    }
  }

  const response = await fetch('/api/contact-submissions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(formData)
  })

  const result = await response.json()
  console.log('Contact form submitted:', result)
  return result
}
```

## 🧪 Testing Scenarios

### 1. Complete Workflow Test

```javascript
async function runCompleteTest() {
  console.log('Starting complete CMS test...')
  
  // 1. Test authentication
  await testLogin()
  
  // 2. Test public endpoints
  await getProjects({ limit: 5 })
  
  // 3. Test contact form
  await testContactForm()
  
  // 4. Test protected endpoints
  await testProtectedEndpoint()
  
  // 5. Test CRUD operations
  const project = await createProject()
  if (project.data) {
    await updateProject(project.data.id, { featured: true })
    // Note: Be careful with delete in testing
    // await deleteProject(project.data.id)
  }
  
  console.log('Complete test finished!')
}

// Run the test
runCompleteTest()
```

### 2. Performance Testing

```javascript
async function performanceTest() {
  const startTime = Date.now()
  
  // Test multiple concurrent requests
  const promises = Array.from({ length: 10 }, () => 
    fetch('/api/projects?limit=20')
      .then(r => r.json())
  )
  
  const results = await Promise.all(promises)
  const endTime = Date.now()
  
  console.log('Performance test results:', {
    requests: 10,
    totalTime: endTime - startTime,
    averageTime: (endTime - startTime) / 10,
    results: results.length
  })
}
```

### 3. Error Handling Test

```javascript
async function errorHandlingTest() {
  // Test invalid endpoints
  const invalidResponse = await fetch('/api/invalid-endpoint')
  console.log('Invalid endpoint:', invalidResponse.status)
  
  // Test malformed data
  const malformedResponse = await fetch('/api/contact-submissions', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      name: '', // Invalid: empty
      email: 'invalid-email', // Invalid: malformed
      subject: 'a'.repeat(201) // Invalid: too long
    })
  })
  
  const errorData = await malformedResponse.json()
  console.log('Validation errors:', errorData)
}
```

## 📊 Monitoring & Debugging

### Browser Developer Tools

1. **Network Tab**: Monitor API requests and responses
2. **Console Tab**: View logs and run test scripts
3. **Application Tab**: Check localStorage and session data
4. **Performance Tab**: Analyze page load and API performance

### Common Issues & Solutions

1. **CORS Errors**: Check server CORS configuration
2. **Authentication Failures**: Verify token format and expiration
3. **Database Connection**: Check PostgreSQL service and credentials
4. **File Upload Issues**: Verify Vercel Blob configuration

### Health Check Endpoint

```javascript
// Test system health
fetch('/api/health')
  .then(response => response.json())
  .then(data => console.log('System health:', data))
```

## 🚀 Production Testing

### Environment Setup

```env
# Production environment variables
NODE_ENV=production
DATABASE_URI=*****************************************************/npi_cms_prod
NEXT_PUBLIC_API_URL=https://your-domain.com
```

### Production Testing Checklist

- [ ] Database connection successful
- [ ] All API endpoints responding
- [ ] Authentication working correctly
- [ ] File uploads functioning
- [ ] Email notifications working
- [ ] Performance within acceptable limits
- [ ] Error handling working properly
- [ ] Security headers configured
- [ ] SSL/TLS certificates valid

This comprehensive guide provides everything needed to test the CMS through the browser with PostgreSQL, including authentication, CRUD operations, and performance monitoring.
